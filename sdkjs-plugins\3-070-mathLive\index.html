<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MathLive 公式编辑器</title>
    <script type="text/javascript" src="../v1/plugins.js"></script>
    <script type="text/javascript" src="../v1/plugins-ui.js"></script>
    <link rel="stylesheet" href="../v1/plugins.css">

    <link rel="stylesheet" href="resources/css/style.css">
    <!-- MathLive 库 -->
    <link rel="preload" href="resources/fonts/KaTeX_AMS-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Caligraphic-Bold.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Caligraphic-Regular.woff2" as="font" crossorigin
        type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Fraktur-Bold.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Fraktur-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Main-BoldItalic.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Main-Bold.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Main-Italic.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Main-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Math-BoldItalic.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Math-Italic.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_SansSerif-Bold.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_SansSerif-Italic.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_SansSerif-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Script-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Size1-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Size2-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Size3-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Size4-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="preload" href="resources/fonts/KaTeX_Typewriter-Regular.woff2" as="font" crossorigin type="font/woff2" />
    <link rel="stylesheet" href="resources/css/mathlive-fonts.css">
    <script src="scripts/mathlive.js"></script>
    <script type="text/javascript" src="scripts/symbolLibrary.js"></script>
    <script type="text/javascript" src="scripts/scripts.js"></script>
</head>

<body>
    <div class="mathlive-container" id="mathlive-container">

        <!-- 工具栏 -->
        <div class="toolbar">
            
            <!-- 分类标签栏 -->
            <div class="category-tabs" id="category-tabs">
                <!-- 分类标签将由 JavaScript 动态生成 -->
            </div>
            <!-- 符号内容区域 -->
            <div id="symbol-content">
                <!-- 符号内容将由 JavaScript 动态生成 -->
            </div>
        </div>
        <div class="mathfield-view">
            <!-- 编辑模式切换工具栏 -->
            <div class="editor-mode-toolbar">
                <div class="mode-switch-container">
                    <button id="visual-mode-btn" class="mode-btn active" title="可视化编辑">
                        <span class="mode-icon">📝</span>
                        <span class="mode-text">可视化</span>
                    </button>
                    <button id="latex-mode-btn" class="mode-btn" title="LaTeX编辑">
                        <span class="mode-icon">📄</span>
                        <span class="mode-text">专业化</span>
                    </button>
                </div>
                <div id="clear-btn">
                    <img src="resources/img/qingchu.svg" alt="">
                    <span>清空</span>
                </div>
            </div>
            
            <!-- 可视化编辑器 -->
            <math-field placeholder="\text{点击可插入公式}" 
                remove-extraneous-parentheses="false"
                style="width: 100%;height: 130px;font-size: 1.7em;overflow: auto;"
                id="mathLive"></math-field>
            
            <!-- LaTeX编辑器 -->
            <textarea id="latexEditor" 
                placeholder="输入LaTeX公式"
                style="display: none; "></textarea>
        </div>
    </div>

    <!-- 矩阵配置对话框 -->
    <div id="matrix-dialog" class="matrix-dialog" style="display: none;">
        <div class="matrix-dialog-content">
            <div class="matrix-dialog-header">
                <h3>矩阵配置</h3>
                <button class="close-btn" id="close-matrix-dialog">&times;</button>
            </div>
            <div class="matrix-dialog-body">
                <div class="matrix-config-row">
                    <label for="matrix-rows">行数:</label>
                    <select id="matrix-rows">
                        <option value="1">1</option>
                        <option value="2" selected>2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                    </select>
                </div>
                <div class="matrix-config-row">
                    <label for="matrix-cols">列数:</label>
                    <select id="matrix-cols">
                        <option value="1">1</option>
                        <option value="2" selected>2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                    </select>
                </div>
                <div class="matrix-config-row">
                    <label for="matrix-brackets">括号类型:</label>
                    <select id="matrix-brackets">
                        <option value="matrix">无括号</option>
                        <option value="pmatrix" selected>圆括号 ( )</option>
                        <option value="bmatrix">方括号 [ ]</option>
                        <option value="Bmatrix">大括号 { }</option>
                    </select>
                </div>
                <div class="matrix-preview">
                    <label>预览:</label>
                    <div id="matrix-preview-content" class="matrix-preview-content"></div>
                </div>
            </div>
            <div class="matrix-dialog-footer">
                <button id="cancel-matrix" class="btn btn-secondary">取消</button>
                <button id="insert-matrix" class="btn btn-primary">插入矩阵</button>
            </div>
        </div>
    </div>

</body>

</html>