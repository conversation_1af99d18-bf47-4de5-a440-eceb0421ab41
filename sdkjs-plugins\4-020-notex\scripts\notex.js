(function (window, undefined) {
  function initPlugin() {
    this.callCommand(
      function () {
        function getCurrentParagraphPosition() {
          var oDocument = Api.GetDocument();
          var currentSentence = oDocument.GetCurrentSentence() || '';
          var searchStr = '^$' + currentSentence;
          oDocument.ReplaceCurrentSentence(searchStr);

          var targetPosition = -1;
          var allParagraphs = oDocument.GetAllParagraphs();

          for (var i = 0; i < allParagraphs.length; i++) {
            var oParagraph = allParagraphs[i];
            var oText = oParagraph.GetText().trim();
            if (oText.includes(searchStr.trim())) {
              targetPosition = i;
              oDocument.ReplaceCurrentSentence(currentSentence);
              break;
            }
          }

          return targetPosition;
        }

        var currentPosition = getCurrentParagraphPosition();

        if (currentPosition == -1) {
          return { error: '未找到当前段落' };
        }

        try {
          let oDocument = Api.GetDocument();
          var allParagraphs = oDocument.GetAllParagraphs();
          var targetParagraph = allParagraphs[currentPosition];
          var hasStyle = targetParagraph.GetStyle() && targetParagraph.GetStyle().GetName() == '标准文件_注×：';
          if (!hasStyle) {
            targetParagraph.SetStyle(oDocument.GetStyle('标准文件_段'));
            let text = targetParagraph.GetText();
            let nParagraph = Api.CreateParagraph();
            nParagraph.AddText(text);
            nParagraph.SetStyle(oDocument.GetStyle('标准文件_注×：'));
            nParagraph.SetIndFirstLine(-447.84);
            let prevParagraph = null;
            for (var i = currentPosition - 1; i >= 0; i--) {
              if (allParagraphs[i].GetNumbering() && allParagraphs[i].GetNumbering().GetClassType() === 'numberingLevel') {
                prevParagraph = allParagraphs[i];
                break;
              }
            }
            if (prevParagraph.GetStyle().GetName() == '标准文件_注×：') {
              var prevNumbering = prevParagraph.GetNumbering().GetNumbering();
              var prevNumberingObj = prevNumbering.GetLevel(prevParagraph.GetNumbering().GetLevelIndex());
              nParagraph.SetNumbering(prevNumberingObj);
            }
            targetParagraph.InsertParagraph(nParagraph, 'after');
            targetParagraph.Delete();
            getCurrentParagraphPosition();
            return;
          }
        } catch (e) {
          return { error: e.message };
        }
      },
      false,
      true,
      function (result) {
        if (result && result.error) {
          this.executeMethod('ShowError', [result.error]);
        }
        this.executeMethod('EndAction', ['Block', 'Save to local storage...', '']);
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    initPlugin.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
