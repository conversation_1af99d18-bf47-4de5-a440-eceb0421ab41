/**
 * 符号库数据
 */

// 符号库数据
const symbolLibrary = {
    '常用': [
        { symbol: '\\frac{\\placeholder{}}{\\placeholder{}}', label: '分数', position: '-2,-2' },
        { symbol: '\\sqrt{\\placeholder{}}', label: '平方根', position: '-2,-101' },
        { symbol: '\\sqrt[\\placeholder{}]{\\placeholder{}}', label: '求根符号', position: '-2,-167' },
        { symbol: '\\placeholder{}^{\\placeholder{}}', label: '上标', position: '-2,-233' },
        { symbol: '\\placeholder{}_{\\placeholder{}}', label: '下标', position: '-2,-300' },
        { symbol: '\\int_\\placeholder{}^\\placeholder{}', label: '定积分', position: '-849,-365' },
        { symbol: '\\int_{\\placeholder{}}', label: '带下标的积分', position: '-849,-431' },
        { symbol: '\\ d\\placeholder{}', label: '微分', position: '-882,-35' },
        { symbol: '\\frac{ d\\placeholder{}}{ d\\placeholder{}}', label: '导数', position: '-882,-134' },
        { symbol: '\\partial', label: '偏微分', position: '-882,-68' },
        { symbol: '\\frac{\\partial\\placeholder{}}{\\partial\\placeholder{}}', label: '偏导数', position: '-882,-200' },

        { symbol: '\\lim_{\\placeholder{}\\rightarrow\\infty}', label: '无穷大', position: '-882,-266' },

        { symbol: '\\left(\\placeholder{}\\right)', label: '括号', position: '-2,-365' },
        { symbol: '\\left|\\placeholder{}\\right|', label: '竖线', position: '-2,-398' },
        { symbol: '\\left[\\placeholder{}\\right]', label: '方括号', position: '-2,-431' },
        { symbol: '\\left\\{\\placeholder{}\\right\\}', label: '花括号', position: '-2,-464' },

        { symbol: '+', label: '加号', position: '3,-492' },
        { symbol: '-', label: '减号', position: '3,-537' },
        { symbol: '\\times', label: '乘号', position: '3,-515' },
        { symbol: '\\div', label: '除号', position: '-30,-20' },
        { symbol: '\\leq', label: '小于等于号', position: '-30,-86' },
        { symbol: '\\geq', label: '大于等于号', position: '-30,-42' },
        { symbol: '\\pm', label: '加减号', position: '-30,3' },
        { symbol: '\\pi', label: '圆周率', position: '-30,-306' },

    ],
    '运算符': [

        { symbol: '\\sum_{\\placeholder{}}^{\\placeholder{}}', label: '上下都有标签的求和符号', position: '-816,-233' },
        { symbol: '\\sum_{\\placeholder{}}', label: '下方有标签的求和符号', position: '-816,-300' },
        { symbol: '\\prod_{\\placeholder{}}^{\\placeholder{}}', label: '上下都有标签的求积符号', position: '-816,-497' },
        { symbol: '\\prod_{\\placeholder{}}', label: '下方有标签的求积符号', position: '-816,-563' },
        { symbol: '\\bigcap', label: '大交集', position: '-849,-299' },
        { symbol: '\\bigcup', label: '大并集', position: '-849,-332' },
        { symbol: '\\prod', label: '求积符号', position: '-849,-200' },
        { symbol: '\\sum', label: '求和符号', position: '-849,-266' },
        { symbol: '\\bigsqcup', label: '大求并运算符', position: '-849,-167' },

        { symbol: '+', label: '加号', position: '3,-492' },
        { symbol: '-', label: '减号', position: '3,-537' },
        { symbol: '\\times', label: '乘号', position: '3,-515' },
        { symbol: '\\div', label: '除号', position: '-30,-20' },
        { symbol: '\\ast', label: '星号', position: '-52,-215' },
        { symbol: '=', label: '等于号', position: '-75,-173' },
        { symbol: '<', label: '小于号', position: '-96,-327' },
        { symbol: '>', label: '大于号', position: '-96,-195' },
        { symbol: '\\neq', label: '不等于号', position: '-73,2' },
        { symbol: '\\approx', label: '约等于号', position: '-73,295' },
        { symbol: '\\leq', label: '小于等于号', position: '-30,-86' },
        { symbol: '\\geq', label: '大于等于号', position: '-30,-42' },
        { symbol: '\\cdot', label: '中间点', position: '-51,-240' },
        { symbol: '\\pm', label: '加减号', position: '-51,-150' },
        { symbol: '\\mp', label: '正负号', position: '-51,-84' },
        { symbol: '\'', label: '撇号', position: '-54,-547' },

        { symbol: '\\pi', label: '圆周率', position: '-30,-306' },
        { symbol: '\\partial', label: '偏微分', position: '-52,-437' },
        { symbol: '\\infty', label: '无穷大符号', position: '-52,-394' },
        { symbol: '\\oplus', label: '循环加号', position: '-161,2' },
        { symbol: '\\otimes', label: '循环乘号', position: '-163,-20' },
        { symbol: '\\in', label: '属于', position: '-118,-261' },
        { symbol: '\\notin', label: '不属于', position: '-94,-460' },
        { symbol: '\\ni', label: '子集', position: '-117,-305' },
        { symbol: '\\cup', label: '并集', position: '-119,-350' },
        { symbol: '\\cap', label: '交集', position: '-119,-371' },
        { symbol: '\\emptyset', label: '空集', position: '-52,-415' },

        { symbol: '\\wedge', label: '逻辑与', position: '-119,-570' },
        { symbol: '\\vee', label: '逻辑或', position: '-140,2' },
        { symbol: '\\neg', label: '“非”符号', position: '-140,-20' },
        { symbol: '\\therefore', label: '所以', position: '-119,-481' },
        { symbol: '\\because', label: '因为', position: '-118,-503' },

        { symbol: '\\rightarrow', label: '向右箭头', position: '-185,-283' },
        { symbol: '\\leftarrow', label: '向左箭头', position: '-185,-261' },
        { symbol: '\\leftrightarrow', label: '左右箭头', position: '-185,-305' },
        { symbol: '\\Rightarrow', label: '向右双箭头', position: '-185,-349' },
        { symbol: '\\Leftarrow', label: '向左双箭头', position: '-185,-327' },
    ],
    '希腊字母': [
        // 小写希腊字母
        { symbol: '\\alpha', label: 'Alpha（小写）', position: '-305,-195' },
        { symbol: '\\beta', label: 'Beta（小写）', position: '-305,-218' },
        { symbol: '\\gamma', label: 'Gamma（小写）', position: '-305,-240' },
        { symbol: '\\delta', label: 'Delta（小写）', position: '-305,-262' },
        { symbol: '\\epsilon', label: 'Epsilon（小写）', position: '-305,-284' },
        { symbol: '\\zeta', label: 'Zeta（小写）', position: '-305,-306' },
        { symbol: '\\eta', label: 'Eta（小写）', position: '-305,-328' },
        { symbol: '\\theta', label: 'Theta（小写）', position: '-305,-348' },
        { symbol: '\\vartheta', label: 'Theta（小写）', position: '-305,-371' },
        { symbol: '\\iota', label: 'Iota（小写）', position: '-305,-394' },
        { symbol: '\\kappa', label: 'Kappa（小写）', position: '-305,-416' },
        { symbol: '\\lambda', label: 'Lambda（小写）', position: '-305,-437' },
        { symbol: '\\mu', label: 'Mu（小写）', position: '-305,-460' },
        { symbol: '\\nu', label: 'Nu（小写）', position: '-305,-482' },
        { symbol: '\\xi', label: 'Xi（小写）', position: '-305,-504' },
        { symbol: '\\pi', label: 'Pi（小写）', position: '-305,-548' },
        { symbol: '\\varpi', label: 'Pi（小写）', position: '-305,-569' },
        { symbol: '\\rho', label: 'Rho（小写）', position: '-326,2' },
        { symbol: '\\varsigma', label: 'Sigma（小写）', position: '-326,-20' },
        { symbol: '\\sigma', label: 'Sigma（小写）', position: '-326,-41' },
        { symbol: '\\tau', label: 'Tau（小写）', position: '-326,-63' },
        { symbol: '\\upsilon', label: 'Upsilon（小写）', position: '-326,-85' },
        { symbol: '\\varphi', label: 'Phi（小写）', position: '-326,-108' },
        { symbol: '\\phi', label: 'Phi（小写）', position: '-326,-130' },
        { symbol: '\\chi', label: 'Chi（小写）', position: '-326,-151' },
        { symbol: '\\psi', label: 'Psi（小写）', position: '-326,-173' },
        { symbol: '\\omega', label: 'Omega（小写）', position: '-326,-195' },

        // 大写希腊字母
        { symbol: '\\Alpha', label: 'Alpha (大写)', position: '-272,-261' },
        { symbol: '\\Beta', label: 'Beta (大写)', position: '-272,-283' },
        { symbol: '\\Gamma', label: 'Gamma (大写)', position: '-272,-305' },
        { symbol: '\\Delta', label: 'Delta (大写)', position: '-272,-327' },
        { symbol: '\\Epsilon', label: 'Epsilon (大写)', position: '-272,-349' },
        { symbol: '\\Zeta', label: 'Zeta (大写)', position: '-272,-371' },
        { symbol: '\\Eta', label: 'Eta (大写)', position: '-272,-393' },
        { symbol: '\\Theta', label: 'Theta (大写)', position: '-272,-415' },
        { symbol: '\\Iota', label: 'Iota (大写)', position: '-272,-437' },
        { symbol: '\\Kappa', label: 'Kappa (大写)', position: '-272,-460' },
        { symbol: '\\Lambda', label: 'Lambda (大写)', position: '-272,-482' },
        { symbol: '\\Mu', label: 'Mu (大写)', position: '-272,-504' },
        { symbol: '\\Nu', label: 'Nu (大写)', position: '-272,-526' },
        { symbol: '\\Xi', label: 'Xi (大写)', position: '-272,-548' },
        { symbol: '\\Pi', label: 'Pi (大写)', position: '-305,2' },
        { symbol: '\\Rho', label: 'Rho (大写)', position: '-305,-20' },
        { symbol: '\\Sigma', label: 'Sigma (大写)', position: '-305,-42' },
        { symbol: '\\Tau', label: 'Tau (大写)', position: '-305,-64' },
        { symbol: '\\Upsilon', label: 'Upsilon (大写)', position: '-305,-86' },
        { symbol: '\\Phi', label: 'Phi (大写)', position: '-305,-108' },
        { symbol: '\\Chi', label: 'Chi (大写)', position: '-305,-130' },
        { symbol: '\\Psi', label: 'Psi (大写)', position: '-305,-151' },
        { symbol: '\\Omega', label: 'Omega (大写)', position: '-305,-173' },
    ],
    '矩阵': [
        { symbol: 'MATRIX_DIALOG', label: '矩阵配置', position: '-552,-552' },
        { symbol: '\\begin{bmatrix}\\placeholder{} & \\placeholder{}\\\\ \\placeholder{} & \\placeholder{}\\end{bmatrix}', label: '带方括号的矩阵', position: '-585,-2' },
        { symbol: '\\begin{pmatrix}\\placeholder{} & \\placeholder{}\\\\ \\placeholder{} & \\placeholder{}\\end{pmatrix}', label: '矩阵3 ()', position: '-585,-68' },

        { symbol: '\\begin{matrix}\\placeholder{}\\\\ \\placeholder{}\\\\ \\placeholder{}\\end{matrix}', label: '3 行1列', position: '-585,-167' },
        { symbol: '\\begin{bmatrix}\\placeholder{}\\\\ \\placeholder{}\\end{bmatrix}', label: '带方括号的 1 行列', position: '-585,-233' },
        { symbol: '\\begin{pmatrix}\\placeholder{}\\\\ \\placeholder{}\\end{pmatrix}', label: '带括号的 1 行列', position: '-585,-299' },

        { symbol: '\\begin{matrix}\\placeholder{} & \\placeholder{} & \\placeholder{}\\end{matrix}', label: '3 列1行', position: '-585,-200' },
        { symbol: '\\begin{bmatrix}\\placeholder{} & \\placeholder{}\\end{bmatrix}', label: '带方括号的 1 列行', position: '-585,-266' },
        { symbol: '\\begin{pmatrix}\\placeholder{} & \\placeholder{}\\end{pmatrix}', label: '带括号的 1 列行', position: '-585,-332' },
        { symbol: '\\begin{array}{rcl}\\placeholder{}&=&\\placeholder{}\\\\ \\placeholder{}&=&\\placeholder{}\\end{array}', label: '对齐方程 ', position: '-585,-563' },

    ],

    '上下标': [
        { symbol: '\\frac{\\placeholder{}}{\\placeholder{}}', label: '分数', position: '-2,-2' },
        { symbol: '\\sqrt{\\placeholder{}}', label: '平方根', position: '-2,-101' },
        { symbol: '\\sqrt[\\placeholder{}]{\\placeholder{}}', label: '求根符号', position: '-2,-167' },
        { symbol: '\\placeholder{}^{\\placeholder{}}', label: '上标', position: '-2,-233' },
        { symbol: '\\placeholder{}_{\\placeholder{}}', label: '下标', position: '-2,-300' },

        { symbol: '{}^{\\placeholder{}}\\placeholder{}', label: '左上标', position: '-651,-164' },
        { symbol: '{}_{\\placeholder{}}\\placeholder{}', label: '左下标', position: '-651,-299' },
        { symbol: '\\placeholder{}_{\\placeholder{}}^{\\placeholder{}}', label: '上标和下标', position: '-651,-101' },
        { symbol: '{}_{\\placeholder{}}^{\\placeholder{}}\\placeholder{}', label: '左下标和上标', position: '-651,-233' },
        { symbol: '\\overset{\\placeholder{}}{\\placeholder{}}', label: '元素上方', position: '-651,-362' },
        { symbol: '\\underset{\\placeholder{}}{\\placeholder{}}', label: '元素下方', position: '-651,-469' },
        { symbol: '\\overset{\\placeholder{}}{\\underset{\\placeholder{}}{\\placeholder{}}}', label: '元素上下方', position: '-651,-398' },

        { symbol: '\\left(\\placeholder{}\\right)', label: '括号', position: '-2,-365' },
        { symbol: '\\left|\\placeholder{}\\right|', label: '竖线', position: '-2,-398' },
        { symbol: '\\left\\langle\\placeholder{}\\right\\rangle', label: '尖括号', position: '-750,-101' },
        { symbol: '\\left[\\placeholder{}\\right]', label: '方括号', position: '-2,-431' },
        { symbol: '\\left\\{\\placeholder{}\\right\\}', label: '花括号', position: '-2,-464' },
        { symbol: '\\overline{\\placeholder{}}', label: '上包围', position: '-783,-167' },
        { symbol: '\\underline{\\placeholder{}}', label: '下包围', position: '-783,-200' },
        { symbol: '\\left|\\placeholder{}\\right.', label: '左包围', position: '-783,-233' },
        { symbol: '\\left.\\placeholder{}\\right|', label: '右包围', position: '-783,-266' },

    ],


    '运算函数': [
        { symbol: '\\int_\\placeholder{}^\\placeholder{}', label: '定积分', position: '-849,-365' },
        { symbol: '\\int_{\\placeholder{}}', label: '带下标的积分', position: '-849,-431' },
        { symbol: '\\int_\\placeholder{}^\\placeholder{}\\placeholder{}\\ d\\placeholder{}', label: '带微分的定积分', position: '-849,-497' },
        { symbol: '\\int_\\placeholder{}\\placeholder{}\\ d\\placeholder{}', label: '带下标和微分的积分', position: '-849,-563' },
         { symbol: '\\ d\\placeholder{}', label: '微分', position: '-882,-35' },
        { symbol: '\\frac{ d\\placeholder{}}{ d\\placeholder{}}', label: '导数', position: '-882,-134' },
        { symbol: '\\partial', label: '偏微分', position: '-882,-68' },
        { symbol: '\\frac{\\partial\\placeholder{}}{\\partial\\placeholder{}}', label: '偏导数', position: '-882,-200' },

        { symbol: '\\lim_{\\placeholder{}\\rightarrow\\infty}', label: '无穷大', position: '-882,-266' },
        { symbol: '\\lim_{\\placeholder{}}', label: '下方有标签的极限', position: '-882,-332' },
        { symbol: '\\nabla\\times\\placeholder{}', label: '旋度', position: '-882,-365' },
        { symbol: '\\nabla\\placeholder{}', label: '梯度', position: '-882,-497' },
        { symbol: '\\nabla\\cdot\\placeholder{}', label: '散度', position: '-882,-431' },

        { symbol: '\\int\\placeholder{}', label: '积分', position: '-915,-103' },
        { symbol: '\\iint\\placeholder{}', label: '二重积分', position: '-915,-169' },
        { symbol: '\\oint\\placeholder{}', label: '围道积分', position: '-915,-137' },

        { symbol: '\\sin', label: '正弦', position: '-948,-200' },
        { symbol: '\\cos', label: '余弦', position: '-948,-266' },
        { symbol: '\\tan', label: '正切', position: '-948,-332' },
        { symbol: '\\csc', label: '余割', position: '-915,-233' },
        { symbol: '\\sec', label: '正割', position: '-915,-299' },
        { symbol: '\\cot', label: '余切', position: '-915,-365' },
        { symbol: '\\log', label: '对数', position: '-948,-398' },
        { symbol: '\\log_\\placeholder{}\\placeholder{}', label: '以 n 为底的对数', position: '-948,-464' },
        { symbol: '\\ln', label: '自然对数', position: '-948,-530' },
        { symbol: '\\sin^{-1}\\left(\\placeholder{}\\right)', label: '反正弦', position: '-915,-431' },
        { symbol: '\\cos^{-1}\\left(\\placeholder{}\\right)', label: '反余弦', position: '-915,-497' },
        { symbol: '\\tan^{-1}\\left(\\placeholder{}\\right)', label: '反正切', position: '-915,-563' },
    ],

    '常用公式': {
        '数学': [
            // 初中数学 (20个)
            { symbol: '|x|', label: '绝对值' },
            { symbol: '\\sqrt{x}', label: '平方根' },
            { symbol: 'x^{2}', label: '平方' },
            { symbol: 'x^{3}', label: '立方' },
            { symbol: 'ax^{2}+bx+c=0', label: '二次方程' },
            { symbol: 'x=\\frac{-b\\pm\\sqrt{b^{2}-4ac}}{2a}', label: '二次方程求根公式' },
            { symbol: '\\Delta=b^{2}-4ac', label: '判别式' },
            { symbol: '\\frac{x^{2}}{a^{2}}+\\frac{y^{2}}{b^{2}}=1', label: '椭圆方程' },
            { symbol: '\\frac{x^{2}}{a^{2}}-\\frac{y^{2}}{b^{2}}=1', label: '双曲线方程' },
            { symbol: 'y^{2}=2px', label: '抛物线方程' },
            { symbol: 'S=\\pi r^{2}', label: '圆面积公式' },
            { symbol: 'C=2\\pi r', label: '圆周长公式' },
            { symbol: 'V=\\frac{1}{3}\\pi r^{2}h', label: '圆锥体积公式' },
            { symbol: 'V=\\frac{4}{3}\\pi r^{3}', label: '球体积公式' },
            { symbol: 'S=4\\pi r^{2}', label: '球表面积公式' },
            { symbol: 'a^{2}+b^{2}=c^{2}', label: '勾股定理' },
            { symbol: '\\sin A=\\frac{a}{c}', label: '正弦定义' },
            { symbol: '\\cos A=\\frac{b}{c}', label: '余弦定义' },
            { symbol: '\\tan A=\\frac{a}{b}', label: '正切定义' },
            { symbol: '\\sin^{2}A+\\cos^{2}A=1', label: '基本三角恒等式' },

            // 高中数学 (20个)
            { symbol: '\\log_{a}(x)', label: '对数' },
            { symbol: '\\ln(x)', label: '自然对数' },
            { symbol: '\\lg(x)', label: '常用对数' },
            { symbol: 'a^{x}', label: '指数函数' },
            { symbol: 'e^{x}', label: '自然指数函数' },
            { symbol: '\\sin(x)', label: '正弦函数' },
            { symbol: '\\cos(x)', label: '余弦函数' },
            { symbol: '\\tan(x)', label: '正切函数' },
            { symbol: '\\sin(A\\pm B)=\\sin A\\cos B\\pm\\cos A\\sin B', label: '正弦和差公式' },
            { symbol: '\\cos(A\\pm B)=\\cos A\\cos B\\mp\\sin A\\sin B', label: '余弦和差公式' },
            { symbol: '\\sin 2A=2\\sin A\\cos A', label: '二倍角正弦公式' },
            { symbol: '\\cos 2A=\\cos^{2}A-\\sin^{2}A', label: '二倍角余弦公式' },
            { symbol: '\\frac{d}{dx}f(x)', label: '导数' },
            { symbol: '\\int f(x)dx', label: '不定积分' },
            { symbol: '\\int_{a}^{b}f(x)dx', label: '定积分' },
            { symbol: '\\lim_{x\\to a}f(x)', label: '极限' },
            { symbol: '\\binom{n}{k}=\\frac{n!}{k!(n-k)!}', label: '组合数' },
            { symbol: 'P(A\\cup B)=P(A)+P(B)-P(A\\cap B)', label: '概率加法公式' },
            { symbol: 'P(A|B)=\\frac{P(A\\cap B)}{P(B)}', label: '条件概率公式' },
            { symbol: 'E[X]=\\sum_{i}x_{i}P(x_{i})', label: '期望值定义' },

            // 高等数学 (20个)
            { symbol: '\\frac{d^{n}}{dx^{n}}f(x)', label: 'n阶导数' },
            { symbol: '\\frac{d}{dx}[f(g(x))]=f\'(g(x))\\cdot g\'(x)', label: '链式法则' },
            { symbol: '\\frac{d}{dx}[f(x)g(x)]=f\'(x)g(x)+f(x)g\'(x)', label: '乘积法则' },
            { symbol: '\\frac{d}{dx}\\left[\\frac{f(x)}{g(x)}\\right]=\\frac{f\'(x)g(x)-f(x)g\'(x)}{[g(x)]^{2}}', label: '商法则' },
            { symbol: '\\int_{a}^{b}f(x)dx=F(b)-F(a)', label: '牛顿-莱布尼茨公式' },
            { symbol: '\\int u\\,dv=uv-\\int v\\,du', label: '分部积分法' },
            { symbol: '\\int f(g(x))g\'(x)dx=\\int f(u)du', label: '换元积分法' },
            { symbol: '\\lim_{x\\to 0}\\frac{\\sin x}{x}=1', label: '重要极限1' },
            { symbol: '\\lim_{x\\to \\infty}\\left(1+\\frac{1}{x}\\right)^{x}=e', label: '重要极限2' },
            { symbol: '\\lim_{x\\to a}\\frac{f(x)}{g(x)}=\\lim_{x\\to a}\\frac{f\'(x)}{g\'(x)}', label: '洛必达法则' },
            { symbol: '\\sum_{n=0}^{\\infty}\\frac{x^{n}}{n!}=e^{x}', label: '指数函数泰勒级数' },
            { symbol: '\\sum_{n=0}^{\\infty}\\frac{(-1)^{n}x^{2n+1}}{(2n+1)!}=\\sin x', label: '正弦函数泰勒级数' },
            { symbol: '\\sum_{n=0}^{\\infty}\\frac{(-1)^{n}x^{2n}}{(2n)!}=\\cos x', label: '余弦函数泰勒级数' },
            { symbol: '\\sum_{n=1}^{\\infty}\\frac{(-1)^{n+1}x^{n}}{n}=\\ln(1+x)', label: '对数函数泰勒级数' },
            { symbol: '\\sum_{n=0}^{\\infty}x^{n}=\\frac{1}{1-x}', label: '几何级数求和' },
            { symbol: '\\det(A)', label: '行列式' },
            { symbol: 'A^{-1}', label: '逆矩阵' },
            { symbol: 'A^{T}', label: '转置矩阵' },
            { symbol: '\\text{rank}(A)', label: '矩阵的秩' },
            { symbol: '\\lambda I-A|=0', label: '特征方程' },
        ],
        '物理': [
            // 力学基础
            { symbol: 'F=ma', label: '牛顿第二定律' },
            { symbol: 'F=G\\frac{m_{1}m_{2}}{r^{2}}', label: '万有引力定律' },
            { symbol: 'v=v_{0}+at', label: '匀加速直线运动速度公式' },
            { symbol: 's=v_{0}t+\\frac{1}{2}at^{2}', label: '匀加速直线运动位移公式' },
            { symbol: 'v^{2}=v_{0}^{2}+2as', label: '匀加速直线运动速度位移关系' },
            { symbol: 'p=mv', label: '动量公式' },
            { symbol: 'F\\Delta t=\\Delta p', label: '动量定理' },
            { symbol: 'E_{k}=\\frac{1}{2}mv^{2}', label: '动能公式' },
            { symbol: 'E_{p}=mgh', label: '重力势能公式' },
            { symbol: 'W=Fs\\cos\\theta', label: '功的定义式' },
            { symbol: 'P=\\frac{W}{t}', label: '功率定义式' },
            { symbol: '\\rho=\\frac{m}{V}', label: '密度公式' },
            { symbol: 'F=\\rho gV', label: '浮力公式' },
            { symbol: 'a=\\frac{v^{2}}{r}', label: '向心加速度公式' },
            { symbol: 'F=m\\frac{v^{2}}{r}', label: '向心力公式' },

            // 振动和波动
            { symbol: 'T=2\\pi\\sqrt{\\frac{l}{g}}', label: '单摆周期公式' },
            { symbol: 'T=2\\pi\\sqrt{\\frac{m}{k}}', label: '弹簧振子周期公式' },
            { symbol: 'f=\\frac{1}{T}', label: '频率与周期关系' },
            { symbol: '\\omega=\\frac{2\\pi}{T}', label: '角频率公式' },
            { symbol: 'v=f\\lambda', label: '波速公式' },
            { symbol: 'y=A\\sin(\\omega t+\\phi)', label: '简谐振动方程' },
            { symbol: 'E=\\frac{1}{2}kA^{2}', label: '简谐振动能量' },

            // 热学
            { symbol: 'PV=nRT', label: '理想气体状态方程' },
            { symbol: 'Q=cm\\Delta T', label: '热量公式' },
            { symbol: '\\Delta U=Q-W', label: '热力学第一定律' },
            { symbol: '\\eta=\\frac{W}{Q_{H}}=1-\\frac{Q_{C}}{Q_{H}}', label: '热机效率' },
            { symbol: '\\Delta S=\\frac{Q_{rev}}{T}', label: '熵变公式' },
            { symbol: 'P=\\sigma A T^{4}', label: '斯特藩-玻尔兹曼定律' },
            { symbol: '\\lambda_{max}T=2.898\\times10^{-3}m\\cdot K', label: '维恩位移定律' },

            // 电学基础
            { symbol: 'F=\\frac{1}{4\\pi\\epsilon_{0}}\\frac{q_{1}q_{2}}{r^{2}}', label: '库仑定律' },
            { symbol: 'E=\\frac{F}{q}', label: '电场强度' },
            { symbol: 'V=\\frac{W}{q}', label: '电势' },
            { symbol: 'U=IR', label: '欧姆定律' },
            { symbol: 'P=UI', label: '电功率公式' },
            { symbol: 'Q=It', label: '电量公式' },
            { symbol: 'P=I^{2}R', label: '焦耳定律' },
            { symbol: 'R=\\rho\\frac{l}{A}', label: '电阻定律' },
            { symbol: 'C=\\frac{Q}{V}', label: '电容' },
            { symbol: 'E=\\frac{1}{2}CV^{2}', label: '电容器储能公式' },

            // 磁学
            { symbol: 'B=\\frac{\\mu_{0}I}{2\\pi r}', label: '长直导线磁场公式' },
            { symbol: 'F=qvB\\sin\\theta', label: '洛伦兹力公式' },
            { symbol: 'F=ILB\\sin\\theta', label: '安培力公式' },
            { symbol: '\\Phi=BA\\cos\\theta', label: '磁通量' },
            { symbol: '\\mathcal{E}=-\\frac{d\\Phi}{dt}', label: '法拉第电磁感应定律' },
            { symbol: 'L=\\frac{\\Phi}{I}', label: '自感系数' },
            { symbol: 'E=\\frac{1}{2}LI^{2}', label: '电感储能' },

            // 光学
            { symbol: 'n=\\frac{c}{v}', label: '折射率公式' },
            { symbol: '\\frac{1}{f}=\\frac{1}{d_{0}}+\\frac{1}{d_{i}}', label: '透镜成像公式' },
            { symbol: '\\frac{\\sin i}{\\sin r}=n', label: '折射定律' },
            { symbol: '\\sin C=\\frac{1}{n}', label: '全反射临界角' },
            { symbol: 'd\\sin\\theta=m\\lambda', label: '光栅衍射公式' },
            { symbol: '\\Delta x=\\frac{\\lambda L}{d}', label: '双缝干涉条纹间距' },

            // 近代物理
            { symbol: 'E=mc^{2}', label: '质能方程' },
            { symbol: 'E=h\\nu', label: '光子能量公式' },
            { symbol: '\\lambda=\\frac{h}{p}', label: '德布罗意波长公式' },
            { symbol: '\\Delta x\\Delta p\\geq\\frac{\\hbar}{2}', label: '海森堡不确定性原理' },
            { symbol: 'E_{n}=-\\frac{13.6}{n^{2}}eV', label: '氢原子能级公式' },
            { symbol: 'R=\\frac{1}{n^{2}}-\\frac{1}{m^{2}}', label: '里德伯公式' },
            { symbol: '\\lambda=\\frac{h}{mv}', label: '德布罗意波长' },
            { symbol: '\\Delta E=\\Delta mc^{2}', label: '质量亏损公式' },

            // 转动
            { symbol: '\\tau=r\\times F', label: '力矩公式' },
            { symbol: 'L=I\\omega', label: '角动量公式' },
            { symbol: 'I=\\sum mr^{2}', label: '转动惯量公式' },
            { symbol: 'E=\\frac{1}{2}I\\omega^{2}', label: '转动动能公式' },
            { symbol: '\\alpha=\\frac{\\tau}{I}', label: '角加速度公式' },

            // 物理常数
            { symbol: 'c=3\\times10^{8}m/s', label: '光速' },
            { symbol: 'h=6.63\\times10^{-34}J\\cdot s', label: '普朗克常数' },
            { symbol: 'e=1.6\\times10^{-19}C', label: '元电荷' },
            { symbol: 'k=9\\times10^{9}N\\cdot m^{2}/C^{2}', label: '静电力常量' },
            { symbol: 'G=6.67\\times10^{-11}N\\cdot m^{2}/kg^{2}', label: '万有引力常量' },
        ],
        '化学': [
            // 基础计算
            { symbol: 'n=\\frac{m}{M}', label: '物质的量公式' },
            { symbol: 'c=\\frac{n}{V}', label: '物质的量浓度公式' },
            { symbol: '\\rho=\\frac{m}{V}', label: '密度公式' },
            { symbol: 'w=\\frac{m_{溶质}}{m_{溶液}}\\times 100\\%', label: '质量分数公式' },
            { symbol: '\\varphi=\\frac{V_{溶质}}{V_{溶液}}\\times 100\\%', label: '体积分数公式' },
            { symbol: 'c_{1}V_{1}=c_{2}V_{2}', label: '稀释公式' },
            { symbol: 'M=\\frac{mRT}{pV}', label: '理想气体摩尔质量' },
            { symbol: '\\eta=\\frac{\\text{实际产量}}{\\text{理论产量}}\\times 100\\%', label: '产率公式' },
            { symbol: '\\text{转化率}=\\frac{\\text{已反应的量}}{\\text{初始量}}\\times 100\\%', label: '转化率公式' },

            // 酸碱理论
            { symbol: 'pH=-\\lg[H^{+}]', label: 'pH值公式' },
            { symbol: 'pOH=-\\lg[OH^{-}]', label: 'pOH值公式' },
            { symbol: 'pH+pOH=14', label: 'pH与pOH关系' },
            { symbol: 'K_{a}=\\frac{[H^{+}][A^{-}]}{[HA]}', label: '弱酸电离常数' },
            { symbol: 'K_{b}=\\frac{[OH^{-}][HB^{+}]}{[B]}', label: '弱碱电离常数' },
            { symbol: 'K_{w}=[H^{+}][OH^{-}]=1.0\\times 10^{-14}', label: '水的离子积常数' },
            { symbol: '\\alpha=\\frac{\\text{已电离的分子数}}{\\text{原有分子总数}}\\times 100\\%', label: '电离度公式' },
            { symbol: '\\alpha=\\sqrt{\\frac{K_{a}}{c}}', label: '稀释定律' },
            { symbol: '[H^{+}]=\\sqrt{K_{a}\\cdot c}', label: '弱酸溶液中氢离子浓度' },
            { symbol: '[OH^{-}]=\\sqrt{K_{b}\\cdot c}', label: '弱碱溶液中氢氧根离子浓度' },
            { symbol: 'pH=\\frac{1}{2}(pK_{a1}+pK_{a2})', label: '多元酸的pH' },

            // 化学平衡
            { symbol: 'K_{c}=\\frac{[C]^{c}[D]^{d}}{[A]^{a}[B]^{b}}', label: '浓度平衡常数' },
            { symbol: 'K_{p}=\\frac{p_{C}^{c}p_{D}^{d}}{p_{A}^{a}p_{B}^{b}}', label: '压力平衡常数' },
            { symbol: 'K_{p}=K_{c}(RT)^{\\Delta n}', label: '平衡常数关系' },
            { symbol: 'K_{sp}=[M^{n+}]^{m}[A^{m-}]^{n}', label: '溶度积常数' },
            { symbol: '\\ln\\frac{K_{2}}{K_{1}}=\\frac{\\Delta H^{\\circ}}{R}\\left(\\frac{1}{T_{1}}-\\frac{1}{T_{2}}\\right)', label: '范特霍夫方程' },

            // 热力学
            { symbol: '\\Delta H=\\sum H_{产物}-\\sum H_{反应物}', label: '反应焓变公式' },
            { symbol: '\\Delta S=\\sum S_{产物}-\\sum S_{反应物}', label: '反应熵变公式' },
            { symbol: '\\Delta G=\\Delta H-T\\Delta S', label: '吉布斯自由能公式' },
            { symbol: '\\Delta G^{\\circ}=-RT\\ln K', label: '标准吉布斯自由能与平衡常数关系' },
            { symbol: '\\Delta G^{\\circ}=\\sum\\Delta G_{f}^{\\circ}(产物)-\\sum\\Delta G_{f}^{\\circ}(反应物)', label: '标准反应自由能' },
            { symbol: '\\Delta U=Q+W', label: '热力学第一定律' },
            { symbol: '\\Delta H=\\Delta U+\\Delta(PV)', label: '焓变与内能变化关系' },
            { symbol: '\\Delta_{mix}S=-R\\sum n_{i}\\ln x_{i}', label: '混合熵' },
            { symbol: 'a_{i}=\\gamma_{i}x_{i}', label: '活度公式' },
            { symbol: '\\Delta G_{mix}=RT\\sum n_{i}\\ln a_{i}', label: '混合吉布斯自由能' },
            { symbol: '\\text{化学势}\\mu=\\mu^{\\circ}+RT\\ln a', label: '化学势公式' },

            // 反应动力学
            { symbol: 'v=k[A]^{m}[B]^{n}', label: '化学反应速率公式' },
            { symbol: '\\ln\\frac{[A]_{0}}{[A]_{t}}=kt', label: '一级反应积分速率方程' },
            { symbol: 't_{1/2}=\\frac{\\ln 2}{k}', label: '一级反应半衰期公式' },
            { symbol: '\\frac{1}{[A]_{t}}=\\frac{1}{[A]_{0}}+kt', label: '二级反应积分速率方程' },
            { symbol: 't_{1/2}=\\frac{1}{k[A]_{0}}', label: '二级反应半衰期公式' },
            { symbol: '[A]_{t}=[A]_{0}-kt', label: '零级反应积分速率方程' },
            { symbol: 't_{1/2}=\\frac{[A]_{0}}{2k}', label: '零级反应半衰期公式' },
            { symbol: 'E_{a}=-R\\frac{d\\ln k}{d(1/T)}', label: '阿伦尼乌斯方程' },
            { symbol: '\\ln k=\\ln A-\\frac{E_{a}}{RT}', label: '阿伦尼乌斯方程积分形式' },
            { symbol: 'k=Ae^{-\\frac{E_{a}}{RT}}', label: '阿伦尼乌斯公式指数形式' },
            { symbol: '\\frac{k_{2}}{k_{1}}=e^{\\frac{E_{a}}{R}\\left(\\frac{1}{T_{1}}-\\frac{1}{T_{2}}\\right)}', label: '温度对反应速率的影响' },

            // 电化学
            { symbol: 'E=E^{\\circ}-\\frac{RT}{nF}\\ln Q', label: '能斯特方程' },
            { symbol: '\\Delta G=-nFE^{\\circ}', label: '标准电动势与自由能关系' },
            { symbol: 'E^{\\circ}=\\frac{RT}{nF}\\ln K', label: '标准电动势与平衡常数关系' },
            { symbol: '\\Delta G^{\\circ}=-nFE^{\\circ}', label: '标准自由能与标准电动势' },
            { symbol: 'Q=I\\times t', label: '法拉第定律' },
            { symbol: 'm=\\frac{M\\times Q}{n\\times F}', label: '电解产物质量公式' },
            { symbol: 'F=96485C/mol', label: '法拉第常数' },
            { symbol: '\\lambda_{m}=\\frac{\\kappa}{c}', label: '摩尔电导率' },
            { symbol: '\\Lambda_{m}^{\\infty}=\\Lambda_{m,+}^{\\infty}+\\Lambda_{m,-}^{\\infty}', label: '科尔劳施定律' },
            { symbol: 't_{+}=\\frac{I_{+}}{I}', label: '离子迁移数' },

            // 溶液性质
            { symbol: 'm=\\frac{\\Delta T_{f}}{K_{f}}', label: '凝固点降低法测摩尔质量' },
            { symbol: 'm=\\frac{\\Delta T_{b}}{K_{b}}', label: '沸点升高法测摩尔质量' },
            { symbol: '\\pi=cRT', label: '渗透压公式' },

            // 常用常数
            { symbol: 'R=8.314J/(mol\\cdot K)', label: '气体常数' },
            { symbol: 'N_{A}=6.022\\times10^{23}mol^{-1}', label: '阿伏伽德罗常数' },
            { symbol: 'k_{B}=1.38\\times10^{-23}J/K', label: '玻尔兹曼常数' },
        ]
    }
};

// 导出符号库（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = symbolLibrary;
}

// 如果在浏览器环境中，将符号库挂载到全局对象
if (typeof window !== 'undefined') {
    window.symbolLibrary = symbolLibrary;
}
