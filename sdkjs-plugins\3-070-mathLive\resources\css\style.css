.mathlive-container {
    position: relative;
    width: 98%;
    height: 490px;
    padding: 10px;
    overflow: auto;
    margin: auto;
    box-sizing: border-box;
}

.mathfield-view .ML__content {
    padding: 4px !important;
    box-sizing: border-box;
}

#mathLive {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    height: 200px;
}

#mathLive:focus-within {
    outline: 1px solid #c5c5c5 !important;
    border-color: transparent;
}

.ML__content-placeholder .ML__text {
    color: #c4c4c4 !important;
    font-size: 16px;
    font-weight: bold;
}


.ML__keyboard {
    box-sizing: border-box;
    height: 380px !important;
    width: calc(100% - 20px) !important;
    position: absolute !important;
    top: 0px !important;
    left: 0px !important;
    --_keyboard-height: 0 !important;
    background-color: #fff !important;
    --_keycap-gap: 5px !important;
}

.ML__keyboard .action {
    justify-content: center !important;
}

.MLK__backdrop {
    background-color: transparent !important;
    box-shadow: none !important;
    border-top: none !important;
}

.MLK__toolbar {
    overflow: visible !important;
}

.MLK__toolbar .MLK__tooltip {
    font-size: 22px !important;
}

.ML__edit-toolbar .action {
    visibility: hidden !important;
}

.mathfield-view {
    width: 100%;
    margin-top: 15px;
    position: relative;
}

/* 编辑模式切换工具栏 */
.editor-mode-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.mode-switch-container {
    display: flex;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 2px;
    border: 1px solid #e0e0e0;
}

.mode-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.2s ease;
    min-width: 60px;
    justify-content: center;
}

.mode-btn:hover {
    background: #e8e8e8;
    color: #333;
}

.mode-btn.active {
    background: #2196f3;
    color: white;
    box-shadow: 0 1px 3px rgba(0, 122, 204, 0.3);
}

.mode-btn.active:hover {
    background: #005a9e;
}

.mode-icon {
    font-size: 14px;
}

.mode-text {
    font-weight: 500;
}

/* LaTeX编辑器样式 */
#latexEditor {
    width: 100%;
    height: 130px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 13px 5px;
    resize: none;
    box-sizing: border-box;
    font-weight: 500;
    font-size: 16px;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

#latexEditor:focus {
    outline: 1px solid #c5c5c5;
    border-color: transparent;
}

@media not (pointer: coarse) {
    math-field::part(virtual-keyboard-toggle) {
        display: none;
    }

    math-field::part(menu-toggle) {
        visibility: hidden;
    }
}



.message {
    position: fixed;
    top: 20px;
    right: 5px;
    padding: 10px 20px;
    border-radius: 5px;
    color: white;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
    font-size: 14px;
}

.message-info {
    background: #17a2b8;
}

.message-warning {
    background: #e6a23c;
}
.message-success {
    background: #28a745;
}

.message-error {
    background: #dc3545;
}

#mathLiveButton {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#mathLiveButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

#mathLiveButton:active {
    transform: translateY(0);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 工具栏样式 */
.toolbar {
    width: 100%;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    position: relative;
}

/* 分类标签栏样式 */
.category-tabs {
    display: flex;
    flex-wrap: wrap;
    border-radius: 6px 6px 0 0;
    padding-bottom: 15px;
}

.category-tab {
    padding: 6px 10px;
    cursor: pointer;
    background-color: #f8f9fa;
    font-size: 14px;
    transition: all 0.2s ease;
    white-space: nowrap;
    color: #495057;
    border-radius: 6px 6px 0 0;
}

.category-tab:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.category-tab.active {
    background-color: #2196f3;
    border-color: #2196f3;
    color: white;
    font-weight: bold;
}

/* 符号分组样式 */
.symbol-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.group-title {
    font-weight: bold;
    color: #495057;
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
    white-space: nowrap;
}

.toolbar button {
    width: 30px;
    height: 30px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    text-align: center;
    background-size: 1368px 600px;
    border: 1px solid transparent;
    padding: 0;
    outline: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.toolbar button:hover {
    border: 1px solid #2196f3;
    transform: translateY(-1px);
}

.toolbar button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

div,
span,
p {
    box-sizing: border-box;
}

#symbol-content {
    max-height: 188px;
    padding-top: 2px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.formula-sub-tabs {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.formula-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    padding: 10px 0;
    height: 150px;
    overflow-y: auto;
}

.formula-sub-tab {
    padding: 0px 16px 6px;
    border-bottom: 2px solid #ddd;
    border: none;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    margin-right: 2px;
    font-size: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.formula-sub-tab.active {
    border-bottom: 2px solid #007bff;
    color: #007bff;
}

.formula-content button {
    width: 113px;
}

#formula-preview {
    position: absolute;
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    max-width: 400px;
    font-family: 'Times New Roman', serif;
}

#formula-preview .title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    text-align: center;
}

/* 矩阵配置对话框样式 */
.matrix-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.matrix-dialog-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 400px;
    max-width: 90%;
    overflow: hidden;
}

.matrix-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.matrix-dialog-header h3 {
    margin: 0;
    color: #333;
    font-size: 14px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: #e0e0e0;
}

.matrix-dialog-body {
    padding: 15px;
    height: 315px;
    overflow-y: auto;
}

.matrix-config-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.matrix-config-row label {
    width: 80px;
    font-weight: 500;
    color: #333;
    margin-right: 10px;
}

.matrix-config-row select {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

.matrix-config-row select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.matrix-preview {
    margin-top: 20px;
}

.matrix-preview label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.matrix-preview-content {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.matrix-preview-content math-field {
    font-size: 1.2em;
    width: 100%;
    min-height: 40px;
}

.matrix-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.matrix-dialog-content .btn {
    padding: 4px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

#clear-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

#clear-btn img {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

